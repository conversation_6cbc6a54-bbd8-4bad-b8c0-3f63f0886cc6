import sqlite3
import pandas as pd
import requests
from datetime import datetime, timezone, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Optional, List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KlineAnalyzer:
    """K线数据分析工具"""
    
    def __init__(self, db_path: str = "ethusdt_klines.db", symbol: str = "ETHUSDT"):
        self.db_path = db_path
        self.symbol = symbol
        self.binance_api_url = "https://api.binance.com/api/v3/klines"
    
    def get_local_data(self, start_time: Optional[datetime] = None, 
                       end_time: Optional[datetime] = None, limit: int = 100) -> pd.DataFrame:
        """从本地数据库获取K线数据"""
        conn = sqlite3.connect(self.db_path)
        
        query = "SELECT * FROM klines"
        params = []
        
        if start_time or end_time:
            conditions = []
            if start_time:
                conditions.append("timestamp >= ?")
                params.append(int(start_time.timestamp() * 1000))
            if end_time:
                conditions.append("timestamp <= ?")
                params.append(int(end_time.timestamp() * 1000))
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        try:
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            if len(df) > 0:
                df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
                df = df.sort_values('timestamp')
                
            return df
        except Exception as e:
            logger.error(f"读取本地数据失败: {e}")
            conn.close()
            return pd.DataFrame()
    
    def get_binance_official_data(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """从币安API获取官方K线数据"""
        try:
            params = {
                'symbol': self.symbol,
                'interval': '5m',
                'startTime': int(start_time.timestamp() * 1000),
                'endTime': int(end_time.timestamp() * 1000),
                'limit': 1000
            }
            
            response = requests.get(self.binance_api_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                return pd.DataFrame()
                
            # 转换为DataFrame
            columns = ['timestamp', 'open_price', 'high_price', 'low_price', 'close_price',
                      'volume', 'close_time', 'quote_asset_volume', 'trade_count',
                      'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore']
            
            df = pd.DataFrame(data, columns=columns)
            
            # 数据类型转换
            numeric_columns = ['open_price', 'high_price', 'low_price', 'close_price', 'volume', 'trade_count']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            df['timestamp'] = pd.to_numeric(df['timestamp'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
            
            return df[['timestamp', 'open_price', 'high_price', 'low_price', 
                      'close_price', 'volume', 'trade_count', 'datetime']]
            
        except Exception as e:
            logger.error(f"获取币安官方数据失败: {e}")
            return pd.DataFrame()
    
    def compare_data(self, hours_back: int = 2) -> dict:
        """对比本地数据和币安官方数据"""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours_back)
        
        logger.info(f"对比时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} 到 {end_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")
        
        # 获取本地数据
        local_df = self.get_local_data(start_time, end_time, limit=500)
        logger.info(f"本地数据条数: {len(local_df)}")
        
        # 获取币安官方数据
        official_df = self.get_binance_official_data(start_time, end_time)
        logger.info(f"官方数据条数: {len(official_df)}")
        
        if len(local_df) == 0 or len(official_df) == 0:
            logger.warning("本地数据或官方数据为空，无法进行比较")
            return {"status": "error", "message": "数据为空"}
        
        # 按时间戳合并数据
        comparison_df = pd.merge(
            local_df[['timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume', 'trade_count']],
            official_df[['timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume', 'trade_count']],
            on='timestamp',
            suffixes=('_local', '_official'),
            how='outer'
        )
        
        # 计算差异
        if len(comparison_df) > 0:
            price_columns = ['open_price', 'high_price', 'low_price', 'close_price']
            for col in price_columns:
                if f'{col}_local' in comparison_df.columns and f'{col}_official' in comparison_df.columns:
                    comparison_df[f'{col}_diff'] = abs(comparison_df[f'{col}_local'] - comparison_df[f'{col}_official'])
                    comparison_df[f'{col}_diff_pct'] = (comparison_df[f'{col}_diff'] / comparison_df[f'{col}_official']) * 100
        
        # 统计结果
        matched_count = len(comparison_df.dropna())
        local_only_count = comparison_df['open_price_official'].isna().sum()
        official_only_count = comparison_df['open_price_local'].isna().sum()
        
        # 计算平均差异
        avg_diffs = {}
        for col in price_columns:
            diff_col = f'{col}_diff'
            if diff_col in comparison_df.columns:
                avg_diffs[col] = {
                    'avg_abs_diff': comparison_df[diff_col].mean(),
                    'max_abs_diff': comparison_df[diff_col].max(),
                    'avg_pct_diff': comparison_df[f'{col}_diff_pct'].mean()
                }
        
        result = {
            "status": "success",
            "comparison_summary": {
                "matched_records": matched_count,
                "local_only_records": local_only_count,
                "official_only_records": official_only_count,
                "total_local_records": len(local_df),
                "total_official_records": len(official_df)
            },
            "price_differences": avg_diffs,
            "comparison_data": comparison_df
        }
        
        return result
    
    def generate_report(self, hours_back: int = 2) -> str:
        """生成数据质量报告"""
        comparison = self.compare_data(hours_back)
        
        if comparison["status"] != "success":
            return f"报告生成失败: {comparison.get('message', '未知错误')}"
        
        summary = comparison["comparison_summary"]
        diffs = comparison["price_differences"]
        
        report = f"""
=== ETH K线数据质量报告 ===
时间范围: 最近 {hours_back} 小时
生成时间: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')} UTC

数据匹配情况:
- 匹配记录数: {summary['matched_records']}
- 仅本地有的记录: {summary['local_only_records']}
- 仅官方有的记录: {summary['official_only_records']}
- 本地总记录数: {summary['total_local_records']}
- 官方总记录数: {summary['total_official_records']}
- 匹配率: {(summary['matched_records'] / max(summary['total_official_records'], 1) * 100):.2f}%

价格差异分析:"""
        
        for price_type, diff_data in diffs.items():
            if diff_data:
                report += f"""
{price_type.replace('_', ' ').title()}:
  - 平均绝对差异: ${diff_data['avg_abs_diff']:.6f}
  - 最大绝对差异: ${diff_data['max_abs_diff']:.6f}
  - 平均百分比差异: {diff_data['avg_pct_diff']:.6f}%"""
        
        return report
    
    def plot_comparison(self, hours_back: int = 2, save_path: Optional[str] = None):
        """绘制本地数据和官方数据的对比图"""
        comparison = self.compare_data(hours_back)
        
        if comparison["status"] != "success":
            logger.error("无法生成对比图，数据获取失败")
            return
        
        df = comparison["comparison_data"]
        
        if len(df) == 0:
            logger.error("无数据可绘制")
            return
        
        # 添加时间列
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
        df = df.sort_values('timestamp')
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'ETH 5分钟K线数据对比 (最近{hours_back}小时)', fontsize=16)
        
        price_types = ['open_price', 'high_price', 'low_price', 'close_price']
        colors = ['blue', 'green', 'red', 'orange']
        
        for i, (price_type, color) in enumerate(zip(price_types, colors)):
            row, col = i // 2, i % 2
            ax = axes[row, col]
            
            # 绘制本地数据和官方数据
            if f'{price_type}_local' in df.columns:
                local_data = df.dropna(subset=[f'{price_type}_local'])
                if len(local_data) > 0:
                    ax.plot(local_data['datetime'], local_data[f'{price_type}_local'], 
                           color=color, alpha=0.7, linewidth=1, label='本地数据')
            
            if f'{price_type}_official' in df.columns:
                official_data = df.dropna(subset=[f'{price_type}_official'])
                if len(official_data) > 0:
                    ax.plot(official_data['datetime'], official_data[f'{price_type}_official'], 
                           color=color, alpha=0.9, linewidth=2, linestyle='--', label='官方数据')
            
            ax.set_title(f'{price_type.replace("_", " ").title()} Price')
            ax.set_xlabel('Time (UTC)')
            ax.set_ylabel('Price (USDT)')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 格式化x轴时间显示
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=1))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"对比图已保存到: {save_path}")
        
        plt.show()
    
    def export_data(self, output_file: str, hours_back: int = 24):
        """导出数据到CSV文件"""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours_back)
        
        df = self.get_local_data(start_time, end_time, limit=5000)
        
        if len(df) == 0:
            logger.warning("没有数据可导出")
            return
        
        df.to_csv(output_file, index=False)
        logger.info(f"数据已导出到: {output_file}, 共 {len(df)} 条记录")

def main():
    """主函数 - 演示分析工具的使用"""
    analyzer = KlineAnalyzer()
    
    print("ETH K线数据分析工具")
    print("=" * 50)
    
    # 生成数据质量报告
    print("正在生成数据质量报告...")
    report = analyzer.generate_report(hours_back=6)
    print(report)
    
    print("\n" + "=" * 50)
    
    # 获取最新的本地数据
    print("最近10条本地K线数据:")
    latest_data = analyzer.get_local_data(limit=10)
    if len(latest_data) > 0:
        display_columns = ['datetime', 'open_price', 'high_price', 'low_price', 
                          'close_price', 'volume', 'trade_count']
        print(latest_data[display_columns].to_string(index=False))
    else:
        print("暂无本地数据")
    
    print("\n" + "=" * 50)
    
    # 选择性功能演示
    while True:
        print("\n可用功能:")
        print("1. 生成数据对比图")
        print("2. 导出数据到CSV")
        print("3. 重新生成报告")
        print("4. 查看指定时间范围的数据")
        print("0. 退出")
        
        choice = input("\n请选择功能 (0-4): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            try:
                hours = int(input("请输入要对比的小时数 (默认2): ") or "2")
                save_plot = input("是否保存图表? (y/n): ").lower().startswith('y')
                save_path = "eth_comparison.png" if save_plot else None
                
                print("正在生成对比图...")
                analyzer.plot_comparison(hours_back=hours, save_path=save_path)
            except ValueError:
                print("请输入有效的数字")
            except Exception as e:
                print(f"生成图表失败: {e}")
                
        elif choice == '2':
            try:
                hours = int(input("请输入要导出的小时数 (默认24): ") or "24")
                filename = input("请输入文件名 (默认eth_data.csv): ") or "eth_data.csv"
                
                analyzer.export_data(filename, hours_back=hours)
            except ValueError:
                print("请输入有效的数字")
            except Exception as e:
                print(f"导出数据失败: {e}")
                
        elif choice == '3':
            try:
                hours = int(input("请输入报告时间范围(小时) (默认6): ") or "6")
                report = analyzer.generate_report(hours_back=hours)
                print(report)
            except ValueError:
                print("请输入有效的数字")
            except Exception as e:
                print(f"生成报告失败: {e}")
                
        elif choice == '4':
            try:
                print("请输入时间范围 (格式: YYYY-MM-DD HH:MM)")
                start_str = input("开始时间 (UTC): ")
                end_str = input("结束时间 (UTC): ")
                
                start_time = datetime.strptime(start_str, '%Y-%m-%d %H:%M').replace(tzinfo=timezone.utc)
                end_time = datetime.strptime(end_str, '%Y-%m-%d %H:%M').replace(tzinfo=timezone.utc)
                
                data = analyzer.get_local_data(start_time, end_time, limit=1000)
                print(f"\n找到 {len(data)} 条记录:")
                if len(data) > 0:
                    print(data[display_columns].to_string(index=False))
                    
            except ValueError:
                print("时间格式错误，请使用 YYYY-MM-DD HH:MM 格式")
            except Exception as e:
                print(f"查询数据失败: {e}")
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()