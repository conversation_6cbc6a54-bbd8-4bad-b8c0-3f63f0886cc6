import asyncio
import json
import sqlite3
import time
import logging
from datetime import datetime, timezone
from typing import Dict, Optional
import websockets
import pandas as pd
from collections import deque
import signal
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('eth_kline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KlineData:
    """K线数据结构"""
    def __init__(self, timestamp: int):
        self.timestamp = timestamp  # K线开始时间(Unix毫秒时间戳)
        self.open_price: Optional[float] = None
        self.high_price: Optional[float] = None
        self.low_price: Optional[float] = None
        self.close_price: Optional[float] = None
        self.volume: float = 0.0
        self.trade_count: int = 0
        
    def update_price(self, price: float, quantity: float):
        """更新价格和成交量"""
        if self.open_price is None:
            self.open_price = price
            
        if self.high_price is None or price > self.high_price:
            self.high_price = price
            
        if self.low_price is None or price < self.low_price:
            self.low_price = price
            
        self.close_price = price
        self.volume += quantity
        self.trade_count += 1

class ETHKlineCollector:
    """ETH K线数据收集器"""
    
    def __init__(self, symbol: str = "ETHUSDT", interval_minutes: int = 5):
        self.symbol = symbol.lower()
        self.interval_minutes = interval_minutes
        self.interval_ms = interval_minutes * 60 * 1000  # 转换为毫秒
        
        # WebSocket URL
        self.ws_url = f"wss://stream.binance.com:9443/ws/{self.symbol}@trade"
        
        # 当前K线数据
        self.current_kline: Optional[KlineData] = None
        self.current_kline_start_time: Optional[int] = None
        
        # 实时交易数据缓存（用于临时存储）
        self.trade_cache = deque(maxlen=100000)  # 最多缓存10万笔交易
        
        # 数据库连接
        self.db_path = f"{symbol.lower()}_klines.db"
        self.init_database()
        
        # 运行状态
        self.running = False
        self.websocket = None
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建K线数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS klines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp INTEGER UNIQUE,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume REAL,
                trade_count INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON klines(timestamp)')
        
        conn.commit()
        conn.close()
        logger.info(f"数据库初始化完成: {self.db_path}")
        
    def get_kline_start_time(self, timestamp: int) -> int:
        """计算给定时间戳所属的K线开始时间"""
        return (timestamp // self.interval_ms) * self.interval_ms
        
    def save_kline(self, kline: KlineData):
        """保存K线数据到数据库"""
        if kline.open_price is None:
            logger.warning("K线数据不完整，跳过保存")
            return
            
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO klines 
                (timestamp, open_price, high_price, low_price, close_price, volume, trade_count)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                kline.timestamp,
                kline.open_price,
                kline.high_price,
                kline.low_price,
                kline.close_price,
                kline.volume,
                kline.trade_count
            ))
            conn.commit()
            
            # 转换时间戳为可读格式
            dt = datetime.fromtimestamp(kline.timestamp / 1000, tz=timezone.utc)
            logger.info(f"K线数据已保存 - 时间: {dt.strftime('%Y-%m-%d %H:%M:%S')} UTC, "
                       f"OHLCV: [{kline.open_price:.4f}, {kline.high_price:.4f}, "
                       f"{kline.low_price:.4f}, {kline.close_price:.4f}, {kline.volume:.4f}], "
                       f"交易次数: {kline.trade_count}")
                       
        except sqlite3.Error as e:
            logger.error(f"保存K线数据失败: {e}")
        finally:
            conn.close()
            
    def cleanup_trade_cache(self, current_kline_start: int):
        """清理不再需要的交易缓存数据"""
        # 保留当前K线周期的数据，清理之前的数据
        cleanup_threshold = current_kline_start
        initial_count = len(self.trade_cache)
        
        # 从左侧移除过期数据
        while self.trade_cache and self.trade_cache[0]['timestamp'] < cleanup_threshold:
            self.trade_cache.popleft()
            
        removed_count = initial_count - len(self.trade_cache)
        if removed_count > 0:
            logger.debug(f"清理了 {removed_count} 条过期交易数据，剩余 {len(self.trade_cache)} 条")
            
    async def process_trade_message(self, message: dict):
        """处理交易消息"""
        try:
            trade_time = int(message['T'])  # 交易时间(毫秒)
            price = float(message['p'])     # 价格
            quantity = float(message['q'])  # 数量
            
            # 计算此交易所属的K线开始时间
            kline_start_time = self.get_kline_start_time(trade_time)
            
            # 将交易数据加入缓存
            trade_data = {
                'timestamp': trade_time,
                'price': price,
                'quantity': quantity,
                'kline_start': kline_start_time
            }
            self.trade_cache.append(trade_data)
            
            # 如果是新的K线周期
            if self.current_kline_start_time != kline_start_time:
                # 保存之前的K线数据
                if self.current_kline is not None:
                    self.save_kline(self.current_kline)
                    # 清理旧的交易缓存
                    self.cleanup_trade_cache(kline_start_time)
                
                # 开始新的K线
                self.current_kline = KlineData(kline_start_time)
                self.current_kline_start_time = kline_start_time
                
                dt = datetime.fromtimestamp(kline_start_time / 1000, tz=timezone.utc)
                logger.info(f"开始新的 {self.interval_minutes} 分钟K线周期: {dt.strftime('%Y-%m-%d %H:%M:%S')} UTC")
            
            # 更新当前K线数据
            if self.current_kline is not None:
                self.current_kline.update_price(price, quantity)
                
        except Exception as e:
            logger.error(f"处理交易消息失败: {e}")
            
    async def websocket_handler(self):
        """WebSocket连接处理"""
        retry_count = 0
        max_retries = 5
        
        while self.running and retry_count < max_retries:
            try:
                logger.info(f"连接 WebSocket: {self.ws_url}")
                
                async with websockets.connect(
                    self.ws_url,
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=10
                ) as websocket:
                    self.websocket = websocket
                    logger.info("WebSocket 连接成功")
                    retry_count = 0  # 重置重试计数
                    
                    async for message in websocket:
                        if not self.running:
                            break
                            
                        try:
                            data = json.loads(message)
                            await self.process_trade_message(data)
                            
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析错误: {e}")
                        except Exception as e:
                            logger.error(f"消息处理错误: {e}")
                            
            except websockets.exceptions.ConnectionClosed:
                logger.warning("WebSocket连接已关闭")
            except websockets.exceptions.InvalidMessage:
                logger.error("WebSocket消息无效")
            except Exception as e:
                logger.error(f"WebSocket连接错误: {e}")
                
            if self.running:
                retry_count += 1
                wait_time = min(2 ** retry_count, 60)  # 指数退避，最大60秒
                logger.info(f"将在 {wait_time} 秒后重试连接... (重试次数: {retry_count}/{max_retries})")
                await asyncio.sleep(wait_time)
                
        if retry_count >= max_retries:
            logger.error("达到最大重试次数，停止重连")
            self.running = False
            
    async def periodic_save(self):
        """定期保存当前K线数据（防止数据丢失）"""
        while self.running:
            await asyncio.sleep(30)  # 每30秒检查一次
            
            if self.current_kline is not None:
                # 检查当前K线是否应该结束了
                current_time = int(time.time() * 1000)
                if current_time >= (self.current_kline_start_time + self.interval_ms):
                    logger.info("当前K线周期已结束，保存数据")
                    self.save_kline(self.current_kline)
                    
                    # 清理缓存
                    next_kline_start = self.get_kline_start_time(current_time)
                    self.cleanup_trade_cache(next_kline_start)
                    
                    # 重置当前K线
                    self.current_kline = None
                    self.current_kline_start_time = None
    
    def get_latest_klines(self, limit: int = 10) -> pd.DataFrame:
        """获取最新的K线数据"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT timestamp, open_price, high_price, low_price, close_price, volume, trade_count
            FROM klines 
            ORDER BY timestamp DESC 
            LIMIT ?
        '''
        
        df = pd.read_sql_query(query, conn, params=(limit,))
        conn.close()
        
        # 转换时间戳为可读格式
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
        
        return df.sort_values('timestamp')
    
    async def start(self):
        """启动数据收集"""
        self.running = True
        logger.info(f"开始收集 {self.symbol.upper()} {self.interval_minutes} 分钟K线数据")
        
        # 创建异步任务
        tasks = [
            asyncio.create_task(self.websocket_handler()),
            asyncio.create_task(self.periodic_save())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            logger.info("收到停止信号")
        finally:
            await self.stop()
    
    async def stop(self):
        """停止数据收集"""
        logger.info("正在停止数据收集...")
        self.running = False
        
        # 保存当前未完成的K线数据
        if self.current_kline is not None:
            self.save_kline(self.current_kline)
            logger.info("已保存未完成的K线数据")
        
        # 关闭WebSocket连接
        if self.websocket:
            await self.websocket.close()
            
        logger.info("数据收集已停止")

def signal_handler(collector):
    """信号处理函数"""
    def handler(signum, frame):
        logger.info(f"收到信号 {signum}，准备停止...")
        asyncio.create_task(collector.stop())
    return handler

async def main():
    """主函数"""
    # 创建收集器实例
    collector = ETHKlineCollector(symbol="ETHUSDT", interval_minutes=5)
    
    # 设置信号处理
    for sig in [signal.SIGINT, signal.SIGTERM]:
        signal.signal(sig, signal_handler(collector))
    
    try:
        # 启动收集器
        await collector.start()
    except Exception as e:
        logger.error(f"程序运行错误: {e}")
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    print("ETH实时K线数据收集器")
    print("按 Ctrl+C 停止程序")
    print("-" * 50)
    
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        
    print("程序已退出")